"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("mo_support_config", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      organization_id: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
        comment: "Organization identifier",
      },
      support_pin: {
        type: Sequelize.STRING(20),
        allowNull: false,
        comment: "PIN required to create support tickets",
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether support is active for this organization",
      },
      allow_attachments: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether file attachments are allowed",
      },
      max_attachment_size: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 5242880, // 5MB
        comment: "Maximum attachment size in bytes",
      },
      allowed_file_types: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: "Array of allowed file extensions",
      },
      auto_assign_enabled: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether to auto-assign tickets to agents",
      },
      default_priority: {
        type: Sequelize.ENUM("low", "medium", "high", "urgent"),
        allowNull: false,
        defaultValue: "medium",
        comment: "Default priority for new tickets",
      },
      sla_response_hours: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 24,
        comment: "SLA response time in hours",
      },
      sla_resolution_hours: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 72,
        comment: "SLA resolution time in hours",
      },
      email_notifications_enabled: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether email notifications are enabled",
      },
      maintenance_mode: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether support is in maintenance mode",
      },
      maintenance_message: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Message to display during maintenance",
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal(
          "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    // Add indexes
    await queryInterface.addIndex("mo_support_config", ["organization_id"]);
    await queryInterface.addIndex("mo_support_config", ["is_active"]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("mo_support_config");
  },
};
