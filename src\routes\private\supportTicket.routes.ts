import express from "express";
import uploadService from "../../helper/upload.service";
import supportTicketController from "../../controller/supportTicket.controller";
import supportTicketValidator from "../../validators/supportTicket.validator";
import { SUPPORT_FILE_UPLOAD_CONSTANT } from "../../helper/common";

const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development",
  SUPPORT_FILE_UPLOAD_CONSTANT.TICKET_ATTACHMENT.folder
);

const router = express.Router();

// GET /list - Get all tickets with pagination and filters (following recipe-ms pattern)
router.get(
  "/list",
  supportTicketValidator.getTicketsListValidator(),
  supportTicketController.getAllTickets
);

// GET /:id - Get single ticket by ID or slug (following recipe-ms pattern)
router.get(
  "/:id",
  supportTicketValidator.getTicketValidator(),
  supportTicketController.getTicket
);

// POST /create - Create new ticket with file upload (following recipe-ms pattern)
/**
 * @swagger
 * /api/v1/private/tickets/create:
 *   post:
 *     tags:
 *       - Support Tickets
 *     summary: Create new support ticket
 *     description: Create a new support ticket with optional file attachments (max 5 files)
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - ticket_title
 *               - ticket_description
 *               - ticket_module
 *               - ticket_type
 *               - ticket_priority
 *               - support_pin
 *             properties:
 *               ticket_title:
 *                 type: string
 *                 maxLength: 200
 *                 description: Ticket title
 *                 example: "Unable to login to system"
 *               ticket_description:
 *                 type: string
 *                 maxLength: 5000
 *                 description: Detailed description of the issue
 *                 example: "I am unable to login to the system with my credentials"
 *               ticket_module:
 *                 type: string
 *                 enum: [auth, hrms, payroll, recruitment, analytics, general]
 *                 description: Module where the issue occurred
 *                 example: "auth"
 *               ticket_type:
 *                 type: string
 *                 enum: [bug, feature_request, support, question, improvement]
 *                 description: Type of ticket
 *                 example: "bug"
 *               ticket_priority:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *                 description: Priority level
 *                 example: "high"
 *               support_pin:
 *                 type: string
 *                 description: Organization support PIN for validation
 *                 example: "1234"
 *               ticketFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Optional file attachments (max 5 files, 50MB each)
 *                 maxItems: 5
 *     responses:
 *       201:
 *         description: Ticket created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ticket created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     ticket_slug:
 *                       type: string
 *                     ticket_title:
 *                       type: string
 *                     ticket_description:
 *                       type: string
 *                     ticket_status:
 *                       type: string
 *                     attachments:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           item_id:
 *                             type: integer
 *                           attachment_name:
 *                             type: string
 *                           file_size:
 *                             type: integer
 *                           mime_type:
 *                             type: string
 *                           attachment_type:
 *                             type: string
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized - Invalid support PIN
 *       500:
 *         description: Internal server error
 */
router.post(
  "/create",
  multerS3Upload?.s3UploadFieldsMiddleware?.([
    { name: "ticketFiles", maxCount: 5 },
  ]) || ((req: any, res: any, next: any) => next()), // Fallback middleware
  supportTicketValidator.createTicketValidator(),
  supportTicketController.createTicket
);

// PUT /update/:id - Update ticket with file upload (following recipe-ms pattern)
/**
 * @swagger
 * /api/v1/private/tickets/update/{id}:
 *   put:
 *     tags:
 *       - Support Tickets
 *     summary: Update support ticket
 *     description: Update an existing support ticket with optional file attachments
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               ticket_title:
 *                 type: string
 *                 maxLength: 200
 *                 description: Updated ticket title
 *               ticket_description:
 *                 type: string
 *                 maxLength: 5000
 *                 description: Updated ticket description
 *               ticket_priority:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *                 description: Updated priority level
 *               ticketFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Additional file attachments (max 5 files, 50MB each)
 *                 maxItems: 5
 *     responses:
 *       200:
 *         description: Ticket updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized access
 *       404:
 *         description: Ticket not found
 *       500:
 *         description: Internal server error
 */
router.put(
  "/update/:id",
  multerS3Upload?.s3UploadFieldsMiddleware?.([
    { name: "ticketFiles", maxCount: 5 },
  ]) || ((req: any, res: any, next: any) => next()), // Fallback middleware
  supportTicketValidator.updateTicketValidator(),
  supportTicketController.updateTicket
);

// DELETE /delete/:id - Delete ticket (following recipe-ms pattern)
router.delete(
  "/delete/:id",
  supportTicketValidator.deleteTicketValidator(),
  supportTicketController.deleteTicket
);

// Removed deprecated /assignable-users and /assign routes as functionality is covered by updateTicket
// PUT /resolve/:id - Resolve ticket
router.put(
  "/resolve/:id",
  supportTicketValidator.resolveTicketValidator(),
  supportTicketController.resolveTicket
);

// POST /rate/:id - Rate ticket
router.post(
  "/rate/:id",
  supportTicketValidator.rateTicketValidator(),
  supportTicketController.rateTicket
);

// GET /:id/comments - Get ticket comments (additional information)
router.get(
  "/:id/comments",
  supportTicketValidator.getTicketCommentsValidator(),
  supportTicketController.getTicketComments
);

// POST /:id/comments - Add comment to ticket (additional information)
router.post(
  "/:id/comments",
  multerS3Upload?.s3UploadFieldsMiddleware?.([
    { name: "attachment", maxCount: 1 },
  ]) || ((req: any, res: any, next: any) => next()), // Fallback middleware
  supportTicketValidator.addTicketCommentValidator(),
  supportTicketController.addTicketComment
);

// GET /:id/history - Ticket history
router.get(
  "/history/:id",
  supportTicketValidator.getTicketHistoryValidator(),
  supportTicketController.getTicketHistory
);

// Export routes following recipe-ms pattern
export default router;
