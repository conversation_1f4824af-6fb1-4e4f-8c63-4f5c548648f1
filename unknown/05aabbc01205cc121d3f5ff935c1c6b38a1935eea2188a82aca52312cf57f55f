import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { db } from "../models";
import { SORT_BY, SORT_ORDER } from "../helper/constant";
import {
  getPagination,
  getPaginatedItems,
  isDefaultAccess,
} from "../utils/common";
import { organizationHelper } from "../helper/organization.helper";

const SupportConfig = db.SupportConfig;

// Special organization ID for default configuration
const DEFAULT_ORG_ID = "DEFAULT";

/**
 * @description Get support configuration for organization with default fallback
 * @route GET /api/v1/private/config/:organization_id
 * @access Private
 */
const getConfig = async (req: Request, res: Response): Promise<any> => {
  try {
    const { organization_id } = req.params;

    // First try to get organization-specific config
    let config = await SupportConfig.findOne({
      where: { organization_id },
      attributes: { exclude: ["support_pin"] },
    });

    // If no org-specific config found, try default config
    if (!config) {
      config = await SupportConfig.findOne({
        where: { organization_id: DEFAULT_ORG_ID },
        attributes: { exclude: ["support_pin"] },
      });

      if (config) {
        // Return default config with actual organization_id for transparency
        const configData = config.toJSON();
        configData.organization_id = organization_id;
        configData.is_default_config = true;

        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("CONFIG_FETCHED_SUCCESSFULLY"),
          data: configData,
        });
      }
    }

    if (!config) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("CONFIG_NOT_FOUND"),
      });
    }

    const configData = config.toJSON();
    configData.is_default_config = false;

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CONFIG_FETCHED_SUCCESSFULLY"),
      data: configData,
    });
  } catch (error) {
    console.error("Error fetching support config:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_CONFIG"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Create or update support configuration (including default config)
 * @route PUT /api/v1/private/config/:organization_id
 * @access Private
 */
const upsertConfig = async (req: Request, res: Response): Promise<any> => {
  try {
    const { organization_id } = req.params;
    const {
      support_pin,
      is_active = true,
      allow_attachments = true,
      max_attachment_size = 5242880,
      allowed_file_types = ["pdf", "png", "jpg", "jpeg", "doc", "docx"],
      auto_assignment_enabled = false,
      sla_response_time_hours = 24,
      sla_resolution_time_hours = 72,
    } = req.body;
    const user_id = (req as any).user?.id || 0;

    // Check if user is trying to create default config
    if (organization_id === DEFAULT_ORG_ID) {
      const hasDefaultAccess = await isDefaultAccess(user_id);
      if (!hasDefaultAccess) {
        return res.status(StatusCodes.FORBIDDEN).json({
          status: false,
          message: res.__("INSUFFICIENT_PERMISSIONS"),
        });
      }
    }

    const [config, isCreated] = await SupportConfig.upsert({
      organization_id,
      support_pin: support_pin || null,
      is_active,
      allow_attachments,
      max_attachment_size,
      allowed_file_types: JSON.stringify(allowed_file_types),
      auto_assignment_enabled,
      sla_response_time_hours,
      sla_resolution_time_hours,
      created_by: user_id,
      updated_by: user_id,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: isCreated
        ? res.__("CONFIG_CREATED_SUCCESSFULLY")
        : res.__("CONFIG_UPDATED_SUCCESSFULLY"),
      data: {
        ...config.toJSON(),
        support_pin: undefined, // Don't expose PIN
        is_default_config: organization_id === DEFAULT_ORG_ID,
      },
    });
  } catch (error) {
    console.error("Error upserting support config:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_UPDATING_CONFIG"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Validate support PIN using Auth microservice
 * @route POST /api/v1/public/config/:organization_id/validate-pin
 * @access Public
 */
const validatePin = async (req: Request, res: Response): Promise<any> => {
  try {
    const { organization_id } = req.params;
    const { support_pin } = req.body;

    // Validate PIN against organization data using database query (replaces Keycloak API)
    const pinValidation = await organizationHelper.validateSupportPinFromDB(
      organization_id,
      support_pin
    );

    if (!pinValidation.isValid) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("INVALID_PIN"),
        error: pinValidation.error,
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("PIN_VALIDATED_SUCCESSFULLY"),
      data: {
        organization_id,
        organization_name: pinValidation.organization?.name,
        pin_valid: true,
      },
    });
  } catch (error) {
    console.error("Error validating support PIN:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_VALIDATING_PIN"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get all configurations (admin only)
 * @route GET /api/v1/private/config
 * @access Private
 */
const getAllConfigs = async (req: Request, res: Response): Promise<any> => {
  try {
    const { page, size, is_active, include_default } = req.query;
    const user_id = (req as any).user?.id || 0;
    const hasDefaultAccess = await isDefaultAccess(user_id);

    if (!hasDefaultAccess) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("INSUFFICIENT_PERMISSIONS"),
      });
    }

    // Get pagination parameters - only apply if explicitly requested
    let limit: number | undefined;
    let offset: number | undefined;
    let isPaginated = false;

    if (page || size) {
      const pagination = getPagination(page as string, size as string);
      limit = pagination.limit;
      offset = pagination.offset;
      isPaginated = true;
    }

    const whereClause: any = {};

    // Filter by active status
    if (is_active !== undefined) {
      whereClause.is_active = is_active === "true";
    }

    // Exclude default config unless specifically requested
    if (include_default !== "true") {
      whereClause.organization_id = { [db.Sequelize.Op.ne]: DEFAULT_ORG_ID };
    }

    // Build query options
    const queryOptions: any = {
      where: whereClause,
      attributes: { exclude: ["support_pin"] }, // Don't expose PINs
      order: [[SORT_BY.CREATED_AT, SORT_ORDER.DESC]],
    };

    // Only add pagination if explicitly requested
    if (isPaginated) {
      queryOptions.limit = limit;
      queryOptions.offset = offset;
    }

    const { rows: configs, count } =
      await SupportConfig.findAndCountAll(queryOptions);

    // Add metadata to configs
    const enrichedConfigs = configs.map((config: any) => {
      const configData = config.toJSON();
      configData.is_default_config =
        configData.organization_id === DEFAULT_ORG_ID;
      return configData;
    });

    // Format response based on whether pagination was requested (CONSISTENT STRUCTURE)
    let responseData;
    if (isPaginated) {
      responseData = getPaginatedItems(
        { count, rows: enrichedConfigs },
        page as string,
        limit!
      );
    } else {
      responseData = {
        configs: enrichedConfigs,
        totalCount: count, // ✅ CONSISTENT: Already using correct field name
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CONFIG_FETCHED_SUCCESSFULLY"),
      data: responseData,
    });
  } catch (error) {
    console.error("Error fetching support configs:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_CONFIG"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Delete support configuration
 * @route DELETE /api/v1/private/config/:organization_id
 * @access Private
 */
const deleteConfig = async (req: Request, res: Response): Promise<any> => {
  try {
    const { organization_id } = req.params;
    const user_id = (req as any).user?.id || 0;

    // Prevent deletion of default config by non-admin users
    if (organization_id === DEFAULT_ORG_ID) {
      const hasDefaultAccess = await isDefaultAccess(user_id);
      if (!hasDefaultAccess) {
        return res.status(StatusCodes.FORBIDDEN).json({
          status: false,
          message: res.__("INSUFFICIENT_PERMISSIONS"),
        });
      }
    }

    const config = await SupportConfig.findOne({
      where: { organization_id },
    });

    if (!config) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("CONFIG_NOT_FOUND"),
      });
    }

    await config.destroy();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_UPDATED"),
    });
  } catch (error) {
    console.error("Error deleting support config:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_UPDATING_CONFIG"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Toggle support status for organization
 * @route PUT /api/v1/private/config/:organization_id/toggle
 * @access Private
 */
const toggleSupport = async (req: Request, res: Response): Promise<any> => {
  try {
    const { organization_id } = req.params;
    const { is_active } = req.body;
    const updated_by = (req as any).user?.id || 0;

    const config = await SupportConfig.findOne({
      where: { organization_id },
    });

    if (!config) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("CONFIG_NOT_FOUND"),
      });
    }

    await config.update({
      is_active,
      updated_by,
    });

    const configData = config.toJSON();
    configData.is_default_config = organization_id === DEFAULT_ORG_ID;

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CONFIG_UPDATED_SUCCESSFULLY"),
      data: {
        ...configData,
        support_pin: undefined,
      },
    });
  } catch (error) {
    console.error("Error toggling support:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_UPDATING_CONFIG"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get default configuration (admin only)
 * @route GET /api/v1/private/config/default
 * @access Private (Admin only)
 */
const getDefaultConfig = async (req: Request, res: Response): Promise<any> => {
  try {
    const user_id = (req as any).user?.id || 0;
    const hasDefaultAccess = await isDefaultAccess(user_id);

    if (!hasDefaultAccess) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("INSUFFICIENT_PERMISSIONS"),
      });
    }

    const config = await SupportConfig.findOne({
      where: { organization_id: DEFAULT_ORG_ID },
      attributes: { exclude: ["support_pin"] },
    });

    if (!config) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("DEFAULT_CONFIG_NOT_FOUND"),
      });
    }

    const configData = config.toJSON();
    configData.is_default_config = true;

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("DEFAULT_CONFIG_FETCHED_SUCCESSFULLY"),
      data: configData,
    });
  } catch (error) {
    console.error("Error fetching default config:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_CONFIG"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Create or update default configuration (admin only)
 * @route PUT /api/v1/private/config/default
 * @access Private (Admin only)
 */
const upsertDefaultConfig = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const user_id = (req as any).user?.id || 0;
    const hasDefaultAccess = await isDefaultAccess(user_id);

    if (!hasDefaultAccess) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("INSUFFICIENT_PERMISSIONS"),
      });
    }

    const {
      is_active = true,
      allow_attachments = true,
      max_attachment_size = 5242880,
      allowed_file_types = ["pdf", "png", "jpg", "jpeg", "doc", "docx"],
      auto_assignment_enabled = false,
      sla_response_time_hours = 24,
      sla_resolution_time_hours = 72,
    } = req.body;

    const [config, isCreated] = await SupportConfig.upsert({
      organization_id: DEFAULT_ORG_ID,
      support_pin: null, // Default configs don't have PINs
      is_active,
      allow_attachments,
      max_attachment_size,
      allowed_file_types: JSON.stringify(allowed_file_types),
      auto_assignment_enabled,
      sla_response_time_hours,
      sla_resolution_time_hours,
      created_by: user_id,
      updated_by: user_id,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: isCreated
        ? res.__("DEFAULT_CONFIG_CREATED_SUCCESSFULLY")
        : res.__("DEFAULT_CONFIG_UPDATED_SUCCESSFULLY"),
      data: {
        ...config.toJSON(),
        support_pin: undefined,
        is_default_config: true,
      },
    });
  } catch (error) {
    console.error("Error upserting default config:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_UPDATING_CONFIG"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

//  - default export with object
export default {
  getConfig,
  upsertConfig,
  validatePin,
  getAllConfigs,
  deleteConfig,
  toggleSupport,
  getDefaultConfig,
  upsertDefaultConfig,
};
