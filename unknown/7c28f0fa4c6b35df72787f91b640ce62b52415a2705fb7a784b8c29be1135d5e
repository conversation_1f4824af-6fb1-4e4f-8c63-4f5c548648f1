"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("mo_support_tickets", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      ticket_slug: {
        type: Sequelize.STRING(20),
        allowNull: false,
        unique: true,
        comment: "Auto-generated unique ticket slug (e.g., TKT-2024-001)",
      },
      organization_id: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: "Organization identifier for multi-tenant support",
      },

      // User identification - store only user_id, fetch details via queries
      ticket_owner_user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: "Reference to nv_users.id - get user details via raw queries",
      },

      // Ticket details
      ticket_title: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: "Brief title/subject of the ticket",
      },
      ticket_description: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: "Detailed description of the issue",
      },
      ticket_module: {
        type: Sequelize.ENUM(
          "GENERAL",
          "TECHNICAL",
          "BILLING",
          "ACCOUNT",
          "FEATURE_REQUEST",
          "BUG_REPORT",
          "TRAINING",
          "INTEGRATION"
        ),
        allowNull: false,
        defaultValue: "GENERAL",
        comment: "Module/category of the ticket",
      },
      ticket_type: {
        type: Sequelize.ENUM(
          "QUESTION",
          "INCIDENT",
          "PROBLEM",
          "TASK",
          "CHANGE_REQUEST"
        ),
        allowNull: false,
        defaultValue: "QUESTION",
        comment: "Type of issue being reported",
      },
      ticket_priority: {
        type: Sequelize.ENUM("LOW", "MEDIUM", "HIGH", "CRITICAL"),
        allowNull: false,
        defaultValue: "MEDIUM",
        comment: "Priority level of the ticket",
      },
      ticket_status: {
        type: Sequelize.ENUM(
          "OPEN",
          "IN_PROGRESS",
          "PENDING_CUSTOMER",
          "PENDING_VENDOR",
          "RESOLVED",
          "CLOSED",
          "CANCELLED"
        ),
        allowNull: false,
        defaultValue: "OPEN",
        comment: "Current status of the ticket",
      },

      // Assignment tracking
      assigned_to_user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: "ID of user assigned to handle the ticket",
      },
      assigned_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Timestamp when ticket was assigned",
      },
      assigned_by_user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: "ID of user who assigned the ticket",
      },

      // Resolution tracking
      resolution_note: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Note explaining how the ticket was resolved",
      },
      resolved_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Timestamp when ticket was resolved",
      },
      resolved_by_user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: "ID of user who resolved the ticket",
      },

      // Rating and review
      rating: {
        type: Sequelize.INTEGER,
        allowNull: true,
        validate: {
          min: 1,
          max: 5,
        },
        comment: "Customer rating (1-5 stars)",
      },
      review_comment: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Customer review/feedback comment",
      },
      rated_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Timestamp when customer provided rating",
      },
      rated_by_user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: "ID of user who provided the rating",
      },

      // SLA tracking
      sla_due_date: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "SLA deadline for resolution",
      },
      first_response_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Timestamp of first agent response",
      },

      // Audit fields
      created_by_user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: "ID of user who created the ticket",
      },
      updated_by_user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: "ID of user who last updated the ticket",
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal(
          "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Soft delete timestamp",
      },
    });

    // Add indexes for performance optimization (following recipe-ms patterns)
    await queryInterface.addIndex("mo_support_tickets", ["organization_id"], {
      name: "idx_tickets_organization_id",
    });

    await queryInterface.addIndex(
      "mo_support_tickets",
      ["ticket_owner_user_id"],
      {
        name: "idx_tickets_owner_user_id",
      }
    );

    await queryInterface.addIndex("mo_support_tickets", ["ticket_status"], {
      name: "idx_tickets_status",
    });

    await queryInterface.addIndex("mo_support_tickets", ["ticket_priority"], {
      name: "idx_tickets_priority",
    });

    await queryInterface.addIndex(
      "mo_support_tickets",
      ["assigned_to_user_id"],
      {
        name: "idx_tickets_assigned_to",
      }
    );

    await queryInterface.addIndex("mo_support_tickets", ["created_at"], {
      name: "idx_tickets_created_at",
    });

    await queryInterface.addIndex("mo_support_tickets", ["deleted_at"], {
      name: "idx_tickets_deleted_at",
    });

    // Composite indexes for common query patterns
    await queryInterface.addIndex(
      "mo_support_tickets",
      ["organization_id", "ticket_status"],
      {
        name: "idx_tickets_org_status",
      }
    );

    await queryInterface.addIndex(
      "mo_support_tickets",
      ["organization_id", "ticket_owner_user_id"],
      {
        name: "idx_tickets_org_owner",
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("mo_support_tickets");
  },
};
