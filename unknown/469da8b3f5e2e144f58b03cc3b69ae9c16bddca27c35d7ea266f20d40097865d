import { sequelize, QueryTypes } from "../models";
import { logger } from "../utils/logger";

/**
 * Organization Helper - Database-based organization operations
 * Following TTH architecture patterns and standards
 *
 * Uses direct database queries for reliability and performance
 */

export interface OrganizationData {
  id: string;
  name: string;
  alias: string;
  enabled: boolean;
  support_pin: string;
  organization_logo?: string;
  attributes?: any;
}

export interface PinValidationResult {
  isValid: boolean;
  organization?: OrganizationData;
  error?: string;
}

class OrganizationHelper {
  /**
   * Validate support PIN using direct database query
   * Queries GROUP_ATTRIBUTE table for organization support PIN
   *
   * @param organizationId - Organization ID to validate PIN for
   * @param providedPin - PIN provided by user
   * @returns Promise<PinValidationResult>
   */
  async validateSupportPinFromDB(
    organizationId: string,
    providedPin: string
  ): Promise<PinValidationResult> {
    const startTime = Date.now();

    try {
      logger.info("Validating support PIN", {
        organizationId,
        operation: "validateSupportPin",
      });

      // Input validation
      if (!organizationId || !providedPin) {
        logger.warn("Missing required parameters for PIN validation", {
          organizationIdProvided: organizationId ? "true" : "false",
          pinProvided: providedPin ? "true" : "false",
        });
        return {
          isValid: false,
          error: "Organization ID and PIN are required",
        };
      }

      // Raw SQL query to get support_pin from GROUP_ATTRIBUTE table
      const query = `
        SELECT 
          ga.VALUE as support_pin, 
          kg.NAME as group_name, 
          kg.ID as group_id
        FROM GROUP_ATTRIBUTE ga
        INNER JOIN KEYCLOAK_GROUP kg ON ga.GROUP_ID = kg.ID  
        WHERE ga.NAME = 'support_pin' 
          AND kg.NAME LIKE :organizationPattern
        LIMIT 1
      `;

      const result = (await sequelize.query(query, {
        replacements: {
          organizationPattern: `%${organizationId}%`,
        },
        type: QueryTypes.SELECT,
      })) as any[];

      const duration = Date.now() - startTime;
      logger.database("PIN_VALIDATION_QUERY", "GROUP_ATTRIBUTE", duration, {
        organizationId,
        recordsFound: result.length,
      });

      // Check if support PIN configuration exists
      if (!result || result.length === 0) {
        logger.warn("Support PIN configuration not found", { organizationId });
        return {
          isValid: false,
          error: "Support PIN not configured for this organization",
        };
      }

      const dbSupportPin = result[0].support_pin;
      const groupName = result[0].group_name;

      // Check if PIN value exists
      if (!dbSupportPin) {
        logger.warn("Support PIN value is empty", {
          organizationId,
          groupName,
        });
        return {
          isValid: false,
          error: "Support PIN not configured for this organization",
        };
      }

      // Validate PIN (plain text comparison)
      const isValid = providedPin === dbSupportPin;

      if (isValid) {
        logger.success("Support PIN validation successful", {
          organizationId,
          groupName,
        });

        // Create organization object for compatibility
        const organization: OrganizationData = {
          id: organizationId,
          name: groupName || organizationId,
          alias: organizationId,
          enabled: true,
          support_pin: dbSupportPin,
          attributes: {
            support_pin: dbSupportPin,
          },
        };

        return {
          isValid: true,
          organization,
        };
      } else {
        logger.security("Invalid support PIN attempt", "medium", {
          organizationId,
          groupName,
        });
        return {
          isValid: false,
          error: "Invalid support PIN",
        };
      }
    } catch (error: any) {
      const duration = Date.now() - startTime;
      logger.error("Failed to validate support PIN", error, {
        organizationId,
        duration,
        operation: "validateSupportPin",
      });

      return {
        isValid: false,
        error: "Failed to validate support PIN due to database error",
      };
    }
  }

  /**
   * Check if organization has support PIN configured
   *
   * @param organizationId - Organization ID to check
   * @returns Promise<boolean>
   */
  async hasSupportPinConfigured(organizationId: string): Promise<boolean> {
    const startTime = Date.now();

    try {
      logger.info("Checking support PIN configuration", { organizationId });

      if (!organizationId) {
        logger.warn("Organization ID required for PIN configuration check");
        return false;
      }

      const query = `
        SELECT COUNT(*) as count
        FROM GROUP_ATTRIBUTE ga
        INNER JOIN KEYCLOAK_GROUP kg ON ga.GROUP_ID = kg.ID  
        WHERE ga.NAME = 'support_pin' 
          AND ga.VALUE IS NOT NULL 
          AND ga.VALUE != ''
          AND kg.NAME LIKE :organizationPattern
      `;

      const result = (await sequelize.query(query, {
        replacements: {
          organizationPattern: `%${organizationId}%`,
        },
        type: QueryTypes.SELECT,
      })) as any[];

      const duration = Date.now() - startTime;
      const hasPin = result[0]?.count > 0;

      logger.database("PIN_CONFIG_CHECK", "GROUP_ATTRIBUTE", duration, {
        organizationId,
        hasConfiguration: hasPin,
      });

      return hasPin;
    } catch (error: any) {
      const duration = Date.now() - startTime;
      logger.error("Failed to check support PIN configuration", error, {
        organizationId,
        duration,
        operation: "hasSupportPinConfigured",
      });
      return false;
    }
  }
}

// Export singleton instance
export const organizationHelper = new OrganizationHelper();
export default organizationHelper;
