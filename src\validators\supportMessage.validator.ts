import { celebrate, Joi, Segments } from "celebrate";
import { MESSAGE_TYPE, VALIDATION_CONSTANT } from "../helper/constant";

// Following recipe-ms pattern with function wrapper
const addMessageValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        message_text: Joi.string()
          .min(1)
          .max(VALIDATION_CONSTANT.MESSAGE_CONTENT_MAX)
          .required(),
        message_type: Joi.string()
          .valid(...Object.values(MESSAGE_TYPE))
          .default(MESSAGE_TYPE.USER),
        is_private: Joi.boolean()
          .default(false)
          .when("message_type", {
            is: MESSAGE_TYPE.USER,
            then: Joi.valid(false).messages({
              "any.only": "is_private cannot be true for USER messages",
            }),
          }),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      ticket_id: Joi.number().integer().positive().required(),
    }),
  });

const getTicketMessagesValidation = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      ticket_id: Joi.number().integer().positive().required(),
    }),
    [Segments.QUERY]: Joi.object()
      .keys({
        page: Joi.number().integer().min(1).optional(),
        limit: Joi.number().integer().min(1).max(100).optional(),
        message_type: Joi.string()
          .valid(...Object.values(MESSAGE_TYPE))
          .optional(),
        is_private: Joi.boolean().optional(),
        include_private: Joi.boolean().optional(),
        since: Joi.date().optional(),
      })
      .unknown(true),
  });

const updateMessageValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        message_text: Joi.string()
          .min(1)
          .max(VALIDATION_CONSTANT.MESSAGE_CONTENT_MAX)
          .optional(),
        is_private: Joi.boolean().optional(),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      ticket_id: Joi.number().integer().positive().required(),
      id: Joi.number().integer().positive().required(),
    }),
  });

const deleteMessageValidation = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      ticket_id: Joi.number().integer().positive().required(),
      id: Joi.number().integer().positive().required(),
    }),
  });

const searchMessagesValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        q: Joi.string().min(1).max(100).required(),
        page: Joi.number().integer().min(1).optional(),
        limit: Joi.number().integer().min(1).max(100).optional(),
        ticket_id: Joi.number().integer().positive().optional(),
        message_type: Joi.string()
          .valid(...Object.values(MESSAGE_TYPE))
          .optional(),
        date_from: Joi.date().optional(),
        date_to: Joi.date().optional(),
      })
      .unknown(true),
  });

export {
  addMessageValidation,
  getTicketMessagesValidation,
  updateMessageValidation,
  deleteMessageValidation,
  searchMessagesValidation,
};

export default {
  addMessageValidation,
  getTicketMessagesValidation,
  updateMessageValidation,
  deleteMessageValidation,
  searchMessagesValidation,
};
