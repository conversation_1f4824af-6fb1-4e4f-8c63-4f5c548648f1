import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { db } from "../models";
import {
  MESSAGE_TYPE,
  TICKET_STATUS,
  SORT_ORDER,
  SORT_BY,
} from "../helper/constant";
// File upload is handled by middleware
import {
  getUser,
  getPagination,
  getPaginatedItems,
  canCreateInternalNotes,
  canViewInternalNotes,
} from "../utils/common";
import * as notificationService from "../services/notification.service";

// Get models from db object to ensure associations are set up (following recipe pattern)
const Ticket = db.Ticket;
const TicketMessage = db.TicketMessage;
const TicketHistory = db.TicketHistory;
const Item = db.Item;

/**
 * @description Add message to ticket with optional file attachment
 * @route POST /api/v1/private/tickets/:ticket_id/messages
 * @access Private (requires authentication)
 */
const addMessage = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ticket_id } = req.params;
    const {
      message_text,
      message_type = MESSAGE_TYPE.USER,
      is_private = false,
    } = req.body;
    const created_by = (req as any).user?.id || 0;

    // Check if user is trying to create an internal note
    if (message_type === MESSAGE_TYPE.INTERNAL_NOTE) {
      const canCreate = await canCreateInternalNotes(created_by);
      if (!canCreate) {
        return res.status(StatusCodes.FORBIDDEN).json({
          status: false,
          message:
            res.__("INTERNAL_NOTE_ACCESS_DENIED") ||
            "Only agents can create internal notes",
        });
      }
    }

    // Verify ticket exists
    const ticket = await Ticket.findByPk(ticket_id);
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    // Handle file upload from S3 middleware (req.files format - same as recipe microservice)
    let attachmentItemId = null;
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      if (files.attachment && files.attachment.length > 0) {
        const uploadedFile = files.attachment[0];
        attachmentItemId = uploadedFile.item_id;
      }
    }

    // For internal notes, always set is_private to true
    const finalIsPrivate =
      message_type === MESSAGE_TYPE.INTERNAL_NOTE ? true : is_private;

    // Create message
    const message = await TicketMessage.create({
      ticket_id: Number(ticket_id),
      message_text,
      message_type,
      is_private: finalIsPrivate,
      attachment_id: attachmentItemId,
      created_by,
    });

    // Update first_response_at if this is the first agent response
    if (message_type === MESSAGE_TYPE.AGENT && !ticket.first_response_at) {
      await ticket.update({
        first_response_at: new Date(),
        updated_by: created_by,
      });

      // Create history record for first response
      await TicketHistory.create({
        ticket_id: ticket.id,
        action_type: "MESSAGE_ADDED",
        previous_status: ticket.ticket_status,
        new_status: ticket.ticket_status,
        change_note: "First response provided by agent",
        created_by,
      });
    }

    // Auto-update ticket status if needed
    if (
      message_type === MESSAGE_TYPE.AGENT &&
      ticket.ticket_status === TICKET_STATUS.OPEN
    ) {
      await ticket.update({
        ticket_status: TICKET_STATUS.IN_PROGRESS,
        updated_by: created_by,
      });

      await TicketHistory.create({
        ticket_id: ticket.id,
        action_type: "STATUS_CHANGED",
        previous_status: TICKET_STATUS.OPEN,
        new_status: TICKET_STATUS.IN_PROGRESS,
        change_note: "Status updated to In Progress after agent response",
        created_by,
      });
    }

    // Add history entry for internal notes
    if (message_type === MESSAGE_TYPE.INTERNAL_NOTE) {
      await TicketHistory.create({
        ticket_id: ticket.id,
        action_type: "INTERNAL_NOTE_ADDED",
        previous_status: ticket.ticket_status,
        new_status: ticket.ticket_status,
        change_note: `Internal note added by agent: ${message_text.substring(0, 100)}${message_text.length > 100 ? "..." : ""}`,
        created_by,
      });
    }

    // Send email notification for new message (using RabbitMQ)
    // Note: Internal notes should NOT trigger email notifications to customers
    if (message_type !== MESSAGE_TYPE.INTERNAL_NOTE) {
      try {
        const messageFromUser = await getUser(created_by);
        const isAgentResponse = message_type === MESSAGE_TYPE.AGENT;

        await notificationService.sendSupportTicketNotification(
          ticket.ticket_owner_email,
          "TICKET_MESSAGE_ADDED",
          {
            ticketNumber: ticket.ticket_number,
            submitter_name: ticket.ticket_owner_name,
            subject: ticket.ticket_title,
            message_text: message_text,
            message_from: messageFromUser?.name || "Unknown User",
            is_agent_response: isAgentResponse,
          }
        );
      } catch (emailError) {
        console.error("Failed to send message notification:", emailError);
        // Don't fail the message creation if email fails
      }
    }

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("MESSAGE_ADDED_SUCCESSFULLY"),
      data: message,
    });
  } catch (error) {
    console.error("Error adding message:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("MESSAGE_ADD_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get messages for a ticket
 * @route GET /api/v1/private/tickets/:ticket_id/messages
 * @access Private (requires authentication)
 */
const getTicketMessages = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ticket_id } = req.params;
    const { include_private, page, limit } = req.query;
    const userId = (req as any).user?.id || 0;

    // Get pagination parameters using utility function
    const pagination = getPagination(page as string, limit as string);
    const isPaginated = !!(page || limit);

    const whereClause: any = { ticket_id: Number(ticket_id) };

    // Check if user can view internal notes (agents only)
    const canViewInternal = await canViewInternalNotes(userId);

    console.log(
      `DEBUG: User ${userId} - canViewInternal: ${canViewInternal}, include_private: ${include_private}`
    );

    // Handle private message filtering
    if (include_private !== "true") {
      // If not including private, exclude private messages
      whereClause.is_private = false;
      console.log(
        `DEBUG: Excluding private messages (include_private != true)`
      );
    } else {
      console.log(`DEBUG: Including private messages (include_private = true)`);
    }
    // If include_private=true, we don't filter by is_private (show all)

    // Filter internal notes based on user permissions
    if (!canViewInternal) {
      // Non-agents cannot see internal notes regardless of include_private setting
      whereClause.message_type = {
        [db.Op.notIn]: [
          MESSAGE_TYPE.INTERNAL_NOTE,
          MESSAGE_TYPE.TICKET_COMMENT,
        ],
      };
      console.log(`DEBUG: Non-agent user - filtering out internal notes`);
    } else {
      // Agents can see all messages except ticket comments (unless specifically requested)
      if (!whereClause.message_type) {
        whereClause.message_type = { [db.Op.ne]: MESSAGE_TYPE.TICKET_COMMENT };
      }
      console.log(
        `DEBUG: Agent user - can see all message types except ticket comments`
      );
    }

    console.log(
      `DEBUG: Final whereClause:`,
      JSON.stringify(whereClause, null, 2)
    );

    // Build query options
    const queryOptions: any = {
      where: whereClause,
      order: [[SORT_BY.CREATED_AT, SORT_ORDER.ASC]],
    };

    // Only add pagination if parameters provided
    if (isPaginated && pagination.limit) {
      queryOptions.limit = pagination.limit;
      queryOptions.offset = pagination.offset;
    }

    const { rows: messages, count } =
      await TicketMessage.findAndCountAll(queryOptions);

    console.log(`DEBUG: Found ${count} messages for ticket ${ticket_id}`);
    console.log(
      `DEBUG: Messages:`,
      messages.map((m: any) => ({
        id: m.id,
        message_type: m.message_type,
        is_private: m.is_private,
        created_by: m.created_by,
      }))
    );

    // Format response using utility function (CONSISTENT STRUCTURE)
    let responseData: any;
    if (isPaginated) {
      responseData = getPaginatedItems(
        { count, rows: messages },
        page as string,
        pagination.limit!
      );
    } else {
      responseData = {
        messages,
        totalCount: count,
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("MESSAGES_FETCHED_SUCCESSFULLY"),
      data: responseData,
    });
  } catch (error) {
    console.error("Error fetching messages:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("MESSAGES_FETCH_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Debug endpoint to check user agent status
 * @route GET /api/v1/private/messages/debug/agent-status
 * @access Private (requires authentication)
 */
const debugAgentStatus = async (req: Request, res: Response): Promise<any> => {
  try {
    const userId = (req as any).user?.id || 0;

    // Import the functions we need
    const { isDefaultAccess, isAgent } = require("../utils/common");

    const canView = await canViewInternalNotes(userId);
    const canCreate = await canCreateInternalNotes(userId);
    const userDetails = await getUser(userId);
    const isDefaultUser = await isDefaultAccess(userId);
    const isAgentUser = await isAgent(userId);

    return res.json({
      status: true,
      data: {
        userId,
        userDetails: {
          id: userDetails?.id,
          name: userDetails?.name,
          email: userDetails?.email,
          organization_id: userDetails?.organization_id,
          keycloak_auth_id: userDetails?.keycloak_auth_id,
        },
        permissions: {
          isDefaultAccess: isDefaultUser,
          isAgent: isAgentUser,
          canViewInternalNotes: canView,
          canCreateInternalNotes: canCreate,
        },
        config: {
          organizationId: process.env.ORGANIZATION_ID,
          superAdminRole: global.config?.KEYCLOAK_SUPER_ADMIN_ROLE,
          superAdminRoleDesc:
            global.config?.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION,
        },
      },
    });
  } catch (error) {
    return res.status(500).json({
      status: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

//  - default export with object
export default {
  addMessage,
  getTicketMessages,
  debugAgentStatus,
};
