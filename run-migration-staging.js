const { exec } = require('child_process');
const path = require('path');

console.log('🚀 Running database migrations on staging environment...');

// Set the working directory to the project root
const projectRoot = __dirname;
process.chdir(projectRoot);

// Run the migration command
const migrationCommand = 'npx sequelize-cli db:migrate --env staging';

console.log(`📁 Working directory: ${process.cwd()}`);
console.log(`⚡ Executing: ${migrationCommand}`);

const migrationProcess = exec(migrationCommand, (error, stdout, stderr) => {
  if (error) {
    console.error('❌ Migration failed with error:', error);
    console.error('Error details:', error.message);
    process.exit(1);
  }

  if (stderr) {
    console.error('⚠️  Migration stderr:', stderr);
  }

  if (stdout) {
    console.log('✅ Migration output:');
    console.log(stdout);
  }

  console.log('🎉 Migration completed successfully!');
  
  // Check migration status after completion
  console.log('\n📊 Checking migration status...');
  exec('npx sequelize-cli db:migrate:status --env staging', (statusError, statusStdout, statusStderr) => {
    if (statusError) {
      console.error('❌ Failed to check migration status:', statusError);
    } else {
      console.log('📋 Migration Status:');
      console.log(statusStdout);
    }
    process.exit(0);
  });
});

// Handle process events
migrationProcess.stdout.on('data', (data) => {
  console.log(data.toString());
});

migrationProcess.stderr.on('data', (data) => {
  console.error(data.toString());
});

migrationProcess.on('close', (code) => {
  console.log(`\n🏁 Migration process exited with code: ${code}`);
});
