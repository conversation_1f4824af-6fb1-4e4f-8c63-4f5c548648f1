import express, { Express } from "express";
import dotenv from "dotenv";
import http from "http";
import cors from "cors";
import morgan from "morgan";
import cookieParser from "cookie-parser";
import swaggerUi from "swagger-ui-express";
import { swaggerSpec } from "./swagger/swagger.config";

// Import utilities and helpers
import i18n from "./utils/i18n";

// Load environment variables
dotenv.config();
const env = process.env.NEXT_NODE_ENV || "development";

import config from "../shared/config/config.json";
import dbconfig from "../shared/config/db.json";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
global.config = JSON.parse(JSON.stringify(config))[env];
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
global.db = JSON.parse(JSON.stringify(dbconfig))[env];

// Environment and database configuration loaded
console.log(`🔧 Environment: ${env}`);

// Import models after global config is set (following recipe-ms pattern)
import { db } from "./models";

// Import routes and middleware after global config is set (like recipe and auth microservices)
import { privateRoutes, publicRoutes } from "./routes";
import userAuth from "./middleware/auth";
import HandleErrorMessage from "./middleware/validatorMessage";

// Import RabbitMQ setup (like recipe and auth microservices)
// import { setupConsumers } from "./rabbitmq/consumerQueue";

// Create Express app and HTTP server (following recipes-ms pattern)
const app: Express = express();
const router = express.Router();
const server = http.createServer(app);

// Basic middleware setup (following recipes-ms standard)
app.use(morgan("combined"));

// Body parsing middleware (following recipes-ms pattern)
router.use(express.json({ limit: "10mb" }));
router.use(express.urlencoded({ extended: true, limit: "10mb" }));

// CORS configuration (simplified to follow recipes-ms standard)
app.use(cors("*"));
router.use(cookieParser());
router.use(i18n.init);

// Request logging (simplified - morgan already handles this)
// Following recipes-ms pattern - no additional request logging needed

// API Routes ()
router.all("/v1/private/*", userAuth);
router.use("/v1/private", privateRoutes);
router.use("/v1/public", publicRoutes);

app.use(router);

function getAllRoutes(app: any) {
  const routes: any = [];

  function formatRoute(path: string) {
    const route = path
      // eslint-disable-next-line no-useless-escape
      .replace(/^\^\\\/?\(\?\=\\\/\|\$\)\^/, "") // Remove leading regex patterns
      // eslint-disable-next-line no-useless-escape
      .replace(/\\\/\?\(\?\=\\\/\|\$\)/g, "") // Remove optional trailing slash regex
      .replace(/\^|\$|\\/g, "") // Remove remaining ^, $, and \
      .replace("(?:/([/]+?))/?", "/:id");
    return route;
  }

  function processStack(stack: any, basePath = "") {
    stack.forEach((layer: any) => {
      if (layer.route) {
        // If it's a direct route
        Object.keys(layer.route.methods).forEach((method) => {
          if (["GET", "POST", "PUT", "DELETE"].includes(method.toUpperCase())) {
            routes.push({
              method: method.toUpperCase(),
              path: formatRoute(basePath + layer.regexp.source),
              params: (layer.route.path.match(/:\w+/g) || []).map(
                (param: string) => param.replace(":", "")
              ),
            });
          }
        });
      } else if (layer.name === "router" && layer.handle.stack) {
        // If it's a router, recurse
        processStack(layer.handle.stack, basePath + layer.regexp.source);
      }
    });
  }

  processStack(app._router.stack);
  return routes;
}

app.get("/", (_req: any, res: any) => {
  const json: any = {
    message: "Welcome to Customer Service Microservice",
    data: getAllRoutes(app),
  };

  return res.send(json);
});

// Handle error message - must be after all routes (following recipes-ms pattern)
app.use(HandleErrorMessage);

// Swagger documentation
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Initialize database and RabbitMQ consumers (following recipes-ms pattern)
const initializeApp = async () => {
  try {
    console.log("🔄 Initializing Support Ticket Microservice...");

    // Initialize database (following recipe-ms pattern)
    await db.sequelize.sync({ alter: true });
    console.log("✅ Database connected and synchronized");

    // Initialize RabbitMQ consumers
    console.log("🔄 Initializing RabbitMQ consumers...");
    // await setupConsumers();
    console.log("✅ RabbitMQ consumers initialized");

    // Start server
    server.listen(global.config.PORT, () => {
      console.log(
        `🚀 Support Ticket Microservice running on port ${global.config.PORT}`
      );
      console.log(
        `📚 API Documentation: http://localhost:${global.config.PORT}/api-docs`
      );
      console.log(`🌐 Server URL: http://localhost:${global.config.PORT}`);
      console.log(`🎯 Ready for API testing!`);
    });
  } catch (error) {
    console.error("❌ Error during application initialization:", error);
    process.exit(1); // Exit the process if initialization fails
  }
};

// Start the application
initializeApp();

export { app, server };
