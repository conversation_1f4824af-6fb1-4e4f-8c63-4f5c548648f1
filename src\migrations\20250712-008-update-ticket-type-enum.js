'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First, add the new enum values to the existing enum
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'bug_report';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'feature_request';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'general_inquiry';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'export_help';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'technical_issue';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'non_technical_issue';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'account_issue';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'billing';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'performance';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'notifications';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_mo_support_tickets_ticket_type" 
      ADD VALUE IF NOT EXISTS 'support';
    `);

    // Update existing records to use new enum values (optional migration of existing data)
    // You can uncomment and modify these if you want to migrate existing data
    /*
    await queryInterface.sequelize.query(`
      UPDATE mo_support_tickets 
      SET ticket_type = 'bug_report' 
      WHERE ticket_type = 'BUG';
    `);
    
    await queryInterface.sequelize.query(`
      UPDATE mo_support_tickets 
      SET ticket_type = 'feature_request' 
      WHERE ticket_type = 'REQUEST_FOR_FEATURE';
    `);
    
    await queryInterface.sequelize.query(`
      UPDATE mo_support_tickets 
      SET ticket_type = 'general_inquiry' 
      WHERE ticket_type = 'GENERAL_QUERY';
    `);
    
    await queryInterface.sequelize.query(`
      UPDATE mo_support_tickets 
      SET ticket_type = 'technical_issue' 
      WHERE ticket_type = 'TECHNICAL';
    `);
    
    await queryInterface.sequelize.query(`
      UPDATE mo_support_tickets 
      SET ticket_type = 'non_technical_issue' 
      WHERE ticket_type = 'NON_TECHNICAL';
    `);
    */
  },

  async down(queryInterface, Sequelize) {
    // Note: PostgreSQL doesn't support removing enum values easily
    // You would need to recreate the enum type to remove values
    // For now, we'll leave the enum values in place
    console.log('Note: PostgreSQL enum values cannot be easily removed. New values will remain in the enum.');
  }
};
