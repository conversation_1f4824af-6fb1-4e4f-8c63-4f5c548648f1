import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface SupportConfigAttributes {
  id?: number;
  organization_id: string;
  support_pin?: string;
  is_active: boolean;
  allow_attachments: boolean;
  max_attachment_size: number;
  allowed_file_types: string; // JSON string
  auto_assignment_enabled: boolean;
  sla_response_time_hours?: number;
  sla_resolution_time_hours?: number;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
}

export default class SupportConfig
  extends Model<SupportConfigAttributes>
  implements SupportConfigAttributes
{
  public id!: number;
  public organization_id!: string;
  public support_pin?: string;
  public is_active!: boolean;
  public allow_attachments!: boolean;
  public max_attachment_size!: number;
  public allowed_file_types!: string;
  public auto_assignment_enabled!: boolean;
  public sla_response_time_hours?: number;
  public sla_resolution_time_hours?: number;
  public created_by?: number;
  public updated_by?: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
  public readonly deleted_at?: Date;

  // Instance methods
  public getAllowedFileTypesArray(): string[] {
    try {
      return JSON.parse(this.allowed_file_types);
    } catch {
      return ["pdf", "png", "jpg", "jpeg", "doc", "docx"];
    }
  }

  public setAllowedFileTypesArray(types: string[]): void {
    this.allowed_file_types = JSON.stringify(types);
  }

  public isFileTypeAllowed(fileType: string): boolean {
    const allowedTypes = this.getAllowedFileTypesArray();
    return allowedTypes.includes(fileType.toLowerCase());
  }

  public isFileSizeAllowed(fileSize: number): boolean {
    return fileSize <= this.max_attachment_size;
  }

  static associate(_models: any) {}
}

// Initialize the model
SupportConfig.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    organization_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: "Tenant-specific configuration",
    },
    support_pin: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: "PIN required to create tickets (optional)",
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: "Enable/disable support for organization",
    },
    allow_attachments: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: "Allow file uploads in tickets",
    },
    max_attachment_size: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 5242880, // 5MB in bytes
      comment: "Maximum file size in bytes",
    },
    allowed_file_types: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: '["pdf", "png", "jpg", "jpeg", "doc", "docx"]',
      comment: "Accepted file types as JSON array",
    },
    auto_assignment_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: "Toggle for automatic ticket assignment",
    },
    sla_response_time_hours: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 24,
      comment: "SLA response time in hours (optional)",
    },
    sla_resolution_time_hours: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 72,
      comment: "SLA resolution time in hours (optional)",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "ID of user who created the configuration",
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "ID of user who last updated the configuration",
    },
  },
  {
    sequelize,
    tableName: "mo_support_configs",
    modelName: "SupportConfig",
    timestamps: true,
    paranoid: true,
    underscored: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    deletedAt: "deleted_at",
    indexes: [
      {
        fields: ["organization_id"],
        unique: true,
      },
      {
        fields: ["is_active"],
      },
      {
        fields: ["created_by"],
      },
    ],
  }
);
