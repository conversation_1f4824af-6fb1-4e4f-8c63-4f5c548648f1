// Test setup file
import dotenv from 'dotenv';

// Load environment variables for testing
dotenv.config();

// Set test environment
process.env.NODE_ENV = 'test';
process.env.ORGANIZATION_ID = 'b7ccd39a-23e9-49e6-8831-a3597a335bb1';

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
